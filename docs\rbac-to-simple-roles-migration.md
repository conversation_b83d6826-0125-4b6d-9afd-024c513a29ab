# RBAC to Simplified Roles Migration

This document outlines the migration from the complex RBAC (Role-Based Access Control) system to a simplified role-based authentication system in CateringHub.

## Migration Overview

**Date**: December 28, 2024
**Migration File**: `supabase/migrations/20241228_remove_rbac_simplify_roles.sql`

## What Was Removed

### Database Components
- **Tables**: `role_permissions`, `provider_role_permissions`
- **Enums**: `app_permission` (13 granular permissions)
- **Functions**: `authorize()`, `has_permission()`
- **Complex RLS policies** using permission-based checks

### Frontend Components
- **Hooks**: `useHasPermission()`, `useUserPermissions()`
- **Permission-based guards**: Complex `PermissionGuard` logic
- **Permission-based navigation**: Navigation filtering using permissions

### Types
- **AppPermission** type and related permission arrays
- **Permission-related exports** from type files

## What Was Added

### Database Components
- **Helper Functions**:
  - `is_admin()` - Returns true if user has admin role
  - `is_provider()` - Returns true if user has catering_provider role
  - `is_provider_owner()` - Returns true if user is a provider owner
  - `get_user_role()` - Returns the user's current role

- **Simplified RLS Policies**:
  - Role-based policies for `profiles` table
  - Role-based policies for `user_roles` table
  - Direct role checking instead of permission checking

- **Updated JWT Hook**:
  - Simplified `custom_access_token_hook()` that only includes role information
  - Removed permission arrays from JWT claims

### Frontend Components
- **Role-based Hooks**:
  - `useIsAdmin()` - Check if user is admin
  - `useIsUser()` - Check if user has user role
  - `useIsProvider()` - Check if user is catering provider
  - `useIsProviderOwner()` - Check if user is provider owner
  - `useIsProviderStaff()` - Check if user is provider staff

- **Simplified Guards**:
  - `RoleGuard` component for role-based access control
  - Backward-compatible `PermissionGuard` that maps to roles

- **Updated Navigation**:
  - Role-based navigation filtering
  - Simplified access control logic

## Role Mapping

The migration maintains the same access patterns but simplifies the implementation:

### Admin Role
- **Old**: Required `users.read`, `users.write`, `settings.read`, etc.
- **New**: Simple `admin` role check

### Catering Provider Role
- **Old**: Required various service/booking permissions based on sub-role
- **New**: Simple `catering_provider` role check with optional owner/staff distinction

### User Role
- **Old**: Basic permissions for authenticated users
- **New**: Simple `user` role (default for all authenticated users)

## Benefits of Simplified System

1. **Reduced Complexity**: Eliminated 13 granular permissions and complex permission checking logic
2. **Better Performance**: Direct role checks instead of permission table joins
3. **Easier Maintenance**: Simpler codebase with fewer moving parts
4. **Clearer Access Control**: Role-based access is more intuitive than permission-based
5. **Faster Development**: Adding new features doesn't require permission mapping

## Data Integrity

- **User Data**: All existing user data was preserved during migration
- **Role Assignments**: Existing role assignments remain intact
- **Access Patterns**: Users maintain the same access levels as before

## Testing Verification

The migration was tested to ensure:
- ✅ Existing users maintain their roles
- ✅ Role-based access control works correctly
- ✅ UI components show/hide appropriately
- ✅ Database access is properly restricted
- ✅ JWT claims contain correct role information

## Usage Examples

### Before (RBAC)
```typescript
const { value: canViewUsers } = useHasPermission("users.read");
if (canViewUsers) {
  // Show users page
}
```

### After (Simplified Roles)
```typescript
const { value: isAdmin } = useIsAdmin();
if (isAdmin) {
  // Show users page
}
```

### Before (Permission Guard)
```tsx
<PermissionGuard permission="users.read">
  <UsersPage />
</PermissionGuard>
```

### After (Role Guard)
```tsx
<RoleGuard roles={["admin"]}>
  <UsersPage />
</RoleGuard>
```

## Migration Impact

- **Breaking Changes**: None for end users
- **Developer Impact**: Simplified API for role checking
- **Performance**: Improved due to fewer database queries
- **Security**: Maintained through simplified RLS policies

This migration successfully transforms CateringHub from a complex RBAC system to a maintainable, production-ready role-based authentication system while preserving all existing functionality and user data.
