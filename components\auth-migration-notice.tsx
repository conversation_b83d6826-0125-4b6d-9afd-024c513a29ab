"use client";

import { <PERSON>ert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { RefreshCw, LogOut } from "lucide-react";
import { createClient } from "@/lib/supabase/client";
import { useRouter } from "next/navigation";

interface AuthMigrationNoticeProps {
  onRetry?: () => void;
}

export function AuthMigrationNotice({ onRetry }: AuthMigrationNoticeProps) {
  const router = useRouter();
  const supabase = createClient();

  const handleSignOut = async () => {
    try {
      await supabase.auth.signOut();
      router.push("/login");
    } catch (error) {
      console.error("Error signing out:", error);
      // Force redirect even if signout fails
      router.push("/login");
    }
  };

  const handleRefresh = () => {
    if (onRetry) {
      onRetry();
    } else {
      window.location.reload();
    }
  };

  return (
    <div className="flex items-center justify-center min-h-[400px] p-4">
      <Alert className="max-w-md">
        <RefreshCw className="h-4 w-4" />
        <AlertTitle>Authentication Update Required</AlertTitle>
        <AlertDescription className="space-y-4">
          <p>
            We've updated our authentication system for better performance and security. 
            Please sign out and sign back in to continue.
          </p>
          <div className="flex gap-2">
            <Button onClick={handleSignOut} className="flex-1">
              <LogOut className="h-4 w-4 mr-2" />
              Sign Out & Re-login
            </Button>
            <Button variant="outline" onClick={handleRefresh}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          </div>
        </AlertDescription>
      </Alert>
    </div>
  );
}
