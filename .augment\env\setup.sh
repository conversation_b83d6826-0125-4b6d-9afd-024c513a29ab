#!/bin/bash
set -e

echo "🚀 Setting up Playwright for Next.js project..."

# Install Node.js 20 (LTS) if not already installed
if ! command -v node &> /dev/null || [[ $(node -v | cut -d'.' -f1 | cut -d'v' -f2) -lt 18 ]]; then
    echo "📦 Installing Node.js 20..."
    curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
    sudo apt-get install -y nodejs
fi

# Install pnpm if not already installed
if ! command -v pnpm &> /dev/null; then
    echo "📦 Installing pnpm..."
    curl -fsSL https://get.pnpm.io/install.sh | sh -
    echo 'export PATH="$HOME/.local/share/pnpm:$PATH"' >> $HOME/.profile
    export PATH="$HOME/.local/share/pnpm:$PATH"
fi

# Navigate to project directory
cd /mnt/persist/workspace

# Install project dependencies first
echo "📦 Installing project dependencies..."
pnpm install

# Add Playwright as dev dependency
echo "📦 Adding Playwright..."
pnpm add -D @playwright/test

# Initialize Playwright configuration
echo "🔧 Initializing Playwright..."
pnpm exec playwright install

# Install Playwright browsers
echo "🌐 Installing Playwright browsers..."
pnpm exec playwright install chromium firefox webkit

# Install system dependencies for browsers
echo "🔧 Installing browser dependencies..."
pnpm exec playwright install-deps

# Create Playwright configuration file
echo "📝 Creating Playwright configuration..."
cat > playwright.config.ts << 'EOF'
import { defineConfig, devices } from '@playwright/test';

/**
 * @see https://playwright.dev/docs/test-configuration
 */
export default defineConfig({
  testDir: './tests',
  /* Run tests in files in parallel */
  fullyParallel: true,
  /* Fail the build on CI if you accidentally left test.only in the source code. */
  forbidOnly: !!process.env.CI,
  /* Retry on CI only */
  retries: process.env.CI ? 2 : 0,
  /* Opt out of parallel tests on CI. */
  workers: process.env.CI ? 1 : undefined,
  /* Reporter to use. See https://playwright.dev/docs/test-reporters */
  reporter: 'html',
  /* Shared settings for all the projects below. See https://playwright.dev/docs/api/class-testoptions. */
  use: {
    /* Base URL to use in actions like `await page.goto('/')`. */
    baseURL: 'http://localhost:3000',

    /* Collect trace when retrying the failed test. See https://playwright.dev/docs/trace-viewer */
    trace: 'on-first-retry',
  },

  /* Configure projects for major browsers */
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },

    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] },
    },

    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] },
    },

    /* Test against mobile viewports. */
    // {
    //   name: 'Mobile Chrome',
    //   use: { ...devices['Pixel 5'] },
    // },
    // {
    //   name: 'Mobile Safari',
    //   use: { ...devices['iPhone 12'] },
    // },

    /* Test against branded browsers. */
    // {
    //   name: 'Microsoft Edge',
    //   use: { ...devices['Desktop Edge'], channel: 'msedge' },
    // },
    // {
    //   name: 'Google Chrome',
    //   use: { ...devices['Desktop Chrome'], channel: 'chrome' },
    // },
  ],

  /* Run your local dev server before starting the tests */
  webServer: {
    command: 'pnpm dev',
    url: 'http://localhost:3000',
    reuseExistingServer: !process.env.CI,
  },
});
EOF

# Create tests directory
echo "📁 Creating tests directory..."
mkdir -p tests

# Create a basic example test
echo "📝 Creating example test..."
cat > tests/example.spec.ts << 'EOF'
import { test, expect } from '@playwright/test';

test('homepage loads correctly', async ({ page }) => {
  await page.goto('/');
  
  // Check if the page loads without errors
  await expect(page).toHaveTitle(/CateringHub/i);
  
  // Check if main content is visible
  await expect(page.locator('body')).toBeVisible();
});

test('navigation works', async ({ page }) => {
  await page.goto('/');
  
  // Wait for page to load
  await page.waitForLoadState('networkidle');
  
  // Check if page is accessible
  await expect(page.locator('body')).toBeVisible();
});
EOF

# Update package.json to include test scripts
echo "📝 Updating package.json with test scripts..."
node -e "
const fs = require('fs');
const pkg = JSON.parse(fs.readFileSync('package.json', 'utf8'));
pkg.scripts = pkg.scripts || {};
pkg.scripts['test'] = 'playwright test';
pkg.scripts['test:ui'] = 'playwright test --ui';
pkg.scripts['test:headed'] = 'playwright test --headed';
pkg.scripts['test:debug'] = 'playwright test --debug';
fs.writeFileSync('package.json', JSON.stringify(pkg, null, 2));
"

# Update .gitignore to include Playwright artifacts
echo "📝 Updating .gitignore..."
if ! grep -q "test-results" .gitignore; then
    echo "" >> .gitignore
    echo "# Playwright" >> .gitignore
    echo "/test-results/" >> .gitignore
    echo "/playwright-report/" >> .gitignore
    echo "/blob-report/" >> .gitignore
    echo "/playwright/.cache/" >> .gitignore
fi

echo "✅ Playwright setup complete!"
echo "📋 Available test commands:"
echo "  pnpm test          - Run all tests"
echo "  pnpm test:ui       - Run tests with UI mode"
echo "  pnpm test:headed   - Run tests in headed mode"
echo "  pnpm test:debug    - Run tests in debug mode"